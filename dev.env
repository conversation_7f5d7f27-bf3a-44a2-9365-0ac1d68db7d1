## Environment ##
NODE_ENV=development
ENVIRONMENT=development
NODE_DEBUG=email-templates node src/app/index.js

LOG_LEVEL=trace

########################################################################################################################
# Analytics:
ANALYTICS_DB_CONNECT_URL=postgres://holger@localhost:5432/mm_analytics
ANALYTICS_DB_USES_SSL=false
ANALYTICS_DB_ENABLE_DEBUG=false
ANALYTICS_DB_POOL_MAX=50
ANALYTICS_DB_POOL_MIN=0
ANALYTICS_QUEUE_PROCESSING_INTERVAL_IN_SECONDS=900
ANALYTICS_QUEUE_BATCH_SIZE=100
ANALYTICS_SYNC_BATCH_SIZE=200
ANALYTICS_AUTOSTART_NEXT_BATCH=false
ANALYTICS_DELETE_EACH_ITEM_FROM_QUEUE=true
ANALYTICS_PAUSE_BETWEEN_BATCHES_MS=100
ANALYTICS_ENABLE_DAILY_SYNC=true
ANALYTICS_SYNC_AT_START_DELAY_IN_SEC=10
ANALYTICS_ENABLE_UNIT_TESTS=false

########################################################################################################################
# API:
# Was 400 until september 5 2024. Increasing so that more than 5 users can be requested by the
# app's Explore screen (8 users -> 560 complexity). 1400 should support 20 users being returned.
API_MAX_QUERY_COMPLEXITY=1400
API_SEND_ASYNC_TASKS_TO_MESSAGE_BUS=true
#API_RATE_LIMITS='[{ "node": "default", "limit": 300, "duration": 60 }]'
#API_RATE_LIMITS='[{ "node": "default", "limit": 300, "duration": 60 }, { "node": "field.query.getMyUser", "limit": 6, "duration": 30 }]'
#API_RATE_LIMITS='[{ "node": "default", "limit": 4, "duration": 30 }, { "node": "type.User", "limit": 2, "duration": 30 }]'
#API_RATE_LIMITS='[{ "node": "type.User", "limit": 2, "duration": 30 }]'
#API_RATE_LIMITS='[{ "node": "default", "limit": 6, "duration": 30 }, { "node": "type.User", "limit": 5, "duration": 30 }, { "node": "field.query.getMyUser", "limit": 4, "duration": 30 }]'
API_RATE_LIMITS='[{ "node": "default", "limit": 30, "duration": 60 }]'
API_USES_AUTHENTICATION=false
########################################################################################################################
## Assets:
ASSETS_AVATAR_S3_BUCKET_USES_ACL="true"
ASSETS_AVATAR_S3_KEY_PREFIX="media/profile-photos"
ASSETS_AVATAR_S3_BUCKET="baragaun-media-development"
ASSETS_AVATAR_CLOUDFRONT_DISTRIBUTION="E2XN36KQ841YVN"
ASSETS_AVATAR_ROOT_URL="https://baragaun-media-development.s3.us-west-2.amazonaws.com"
ASSETS_AVATAR_MAX_IMAGE_WIDTH="600"
ASSETS_AVATAR_MAX_IMAGE_HEIGHT="600"

########################################################################################################################
# Admin:
ADMIN_ENABLE_ADMIN_UI=true

########################################################################################################################
# AdminJs:
ENABLE_SERVICE_ADMINJS=true
ADMINJS_ROOT=/admin/adminjs

########################################################################################################################
## Authentication ##
COOKIE_DOMAIN=localhost
COOKIE_PATH=/
SECURE_COOKIE=false
COOKIE_SECRET=xxxxxxxxxxxxxx
# expires in 3 days
COOKIE_EXPIRATION=259200000

########################################################################################################################
# AWS:
#   IAM user 'mm3-s3-unit-tests':
#     AWS_SES_ACCESS_KEY_ID="********************"
#     AWS_SES_SECRET_ACCESS_KEY="iJYDQzJRlW/ls25Puen1xy2b2oLymSCC7LpDDVrH"
#   IAM user 'holger':
#     AWS_S3_ACCESS_KEY_ID="********************"
#     AWS_S3_SECRET_ACCESS_KEY="W2w2pUglZc8P29Y+i/bHzz0aYwJYOQAxHEVDgZL9"
#   IAM user 'mm-s3-user-dev':
#     AWS_S3_ACCESS_KEY_ID=********************
#     AWS_S3_SECRET_ACCESS_KEY=s3pq1nSL2RUitdqgsOEr88kk22/US5f8rrKuU8HC
#     Access to bucket: baragaun-media-dev
#   CloudFront distribution (dev): E3GF4UG1C8VL5E
#   https://us-east-1.console.aws.amazon.com/cloudfront/v4/home?region=us-west-2#/distributions/E3GF4UG1C8VL5E
#   URL: https://dro5a141wxkcj.cloudfront.net
#   Bucket: baragaun-media-dev

############
# CDN:
CDN_ROOT_URI=https://org-baragaun-cdn-development.s3.us-west-2.amazonaws.com
AWS_CLOUDFRONT_DISTRIBUTION_ID=E2XN36KQ841YVN
CDN_ROOT_URI=https://dit7rgmgk3zkw.cloudfront.net/
# Messaging:
MESSAGING_MULTI_ACTION_SEND_METHOD_PRIORITY=email

############
# SES:
# Micromentor keys?
AWS_SES_ACCESS_KEY_ID=********************
AWS_SES_SECRET_ACCESS_KEY=6DyThNpepXGaf+ZTwuZmN/ig5Mql3KL/0/K1Qrj1
#AWS_SES_REGION=us-west-2

# IAM user first-spark-server-dev:
#AWS_SES_ACCESS_KEY_ID=********************
#AWS_SES_SECRET_ACCESS_KEY=oRLkGaJmpol+CPKoMdK0OdVGa+kzBwtj7soEl7Wu

############
# S3:
#AWS_S3_ACCESS_KEY_ID="********************"
#AWS_S3_SECRET_ACCESS_KEY="s3pq1nSL2RUitdqgsOEr88kk22/US5f8rrKuU8HC"
#AWS_S3_REGION="us-west-2"
#AWS_S3_CDN_BUCKET="baragaun-media-test"

AWS_S3_ACCESS_KEY_ID=********************
AWS_S3_SECRET_ACCESS_KEY=EupDr8Je3KfFRqDNckc81n0nJsrJRgyfKE1TMGz1
AWS_S3_REGION=us-west-2
AWS_S3_CDN_BUCKET=baragaun-media-development

# mm2 development bucket info
AWS_S3_MM2_REGION=us-west-2
AWS_S3_MM2_CDN_BUCKET=baragaun-media-development
MM2_AWS_S3_BUCKET=baragaun-media-development
MM2_AWS_S3_BUCKET_REGION=us-west-2
MM2_AWS_S3_FILE_PREFIX_GENERATED_IMAGES=media/generated/
MM2_AWS_S3_FILE_PREFIX_ORIGINAL_IMAGES=media/
MM2_AWS_CLOUDFRONT_DISTRIBUTION_ID=E2XN36KQ841YVN

########################################################################################################################
# Background Tasks:
REDIS_BACKGROUND_TASKS_DB_INDEX=4
BULL_BOARD_PATH="/fsdata/admin/tasks"

########################################################################################################################
# BgVault:
BG_VAULT_OWNER=mm
BG_VAULT_NODE_CONFIGS='[{"nodeId":"KZ01","countryCode":"KZ","postUrl":"https://bgvault-kz01.baragaun.xyz/save","hmacSecret":"bd572f548ee051a77946925af7907065cd1d7b25b481c7214fdcfa212b028432","encryptionKey":"MJhx8b7quFndOF9j","encryptionIv":"F0LTtWPtwmdiWkHL","cypherId":"2004-2"},{"nodeId":"UZ01","countryCode":"UZ","postUrl":"https://bgvault-uz01.baragaun.xyz/save","hmacSecret":"5c456581a6a197b0a487935fb8b452600f8b2aff85bd2e685c7a54fe79217919","encryptionKey":"MJhx8b7quFndOF9j","encryptionIv":"F0LTtWPtwmdiWkHL","cypherId":"2004-2"}]'

########################################################################################################################
# Express HTTP server:
SESSION_SECRET=c2a59c7410d74becb3f274791fadee36a8ea2dc749ca4f91945a54e8ca58f72e5446743b07ac4926a99d9fab799636ec
JWT_SECRET=b7745385a05a4ef09915294783e1324ea36870cffb7f43a1a206a094663fb4202f63be25ba324b65bf41a0f93e217d89
# JWT_EXPIRES_IN=120d
HTTP_SERVER_HOST=localhost
HTTP_SERVER_PORT=8092
HTTP_SERVER_BASE_URL=http://localhost:8092/fsdata
HMAC_SECRET=test-46efe6f41b5e42c4b3f5369eb3bbc87158b10e1e74844c04ad3c6a849a3274c6
HMAC_KEY=test-
KNOWN_ACCESS_TOKENS=5b0e5d1998ac4c23b014f620998f371576f9c1e4b87e4d4c8c584af4a890c252
USER_INFO_STORE_AES_KEY=test-
HTTP_FILE_UPLOAD_DIR="/Users/<USER>/src/baragaun/fsdata/_hs_local/file-upload"
HTTP_ENABLE_AUTH=false
HTTP_DEBUG_AUTH=true
HTTP_LOG_REQUESTS=true

########################################################################################################################
# Apple
APPLE_KEY_ID=""
APPLE_TEAM_ID=""
APPLE_CLIENT_ID=""
APPLE_SECRET=""

########################################################################################################################
# Facebook
FACEBOOK_APP_ID=""
FACEBOOK_APP_SECRET=""

########################################################################################################################
# Firebase
******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

########################################################################################################################
# Google
GOOGLE_API_KEY="AIzaSyAJVsWRAnxitdqbHRw0jIcZ72BbzrNAob4"
GOOGLE_OAUTH2_SERVER_CLIENT_ID="************-qro952dvrj232q54b3ma1pbpman5emeo.apps.googleusercontent.com"
GOOGLE_OAUTH2_SERVER_CLIENT_SECRET="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"
GOOGLE_OAUTH2_ANDROID_CLIENT_ID=""
GOOGLE_OAUTH2_IOS_CLIENT_ID="************-irvl2ndbpsuv1sseigk362gqlphrpcq6.apps.googleusercontent.com"
GOOGLE_OAUTH2_WEB_CLIENT_ID="************-iquik4khfhd6c3cfjrcbdr4al2dg0m9i.apps.googleusercontent.com"

########################################################################################################################
# Twitter
TWITTER_CLIENT_ID=""
TWITTER_SECRET=""

########################################################################################################################
# MessageBus:
MESSAGE_BUS_TECH=bull
BULL_BOARD_PATH="/admin/message-bus"
# MESSAGE_BUS_TECH=rabbitmq
# MESSAGE_BUS_CONNECT_URI=amqp://rabbitmq:5672
# MESSAGE_BUS_PROCESS_ASYNC_TASK_MESSAGES=false

########################################################################################################################
# Messaging:
EMAIL_TRANSPORT_SERVICE=ses
EMAIL_SENDER_ADDRESS=*******
#EMAIL_SENDER_ADDRESS=*******
EMAIL_SENDER_NAME="Baragaun Team"
MESSAGING_MULTI_ACTION_SEND_METHOD_PRIORITY=email

########################################################################################################################
# MongoDB:
MAIN_DB_CONNECT_URI=mongodb://127.0.0.1:27017
MAIN_DB_NAME=first-spark-dev
ENABLE_DB_DEBUG=false

########################################################################################################################
# NATS
NATS_CONNECT_JSON='{servers:"127.0.0.1:4442"}'

########################################################################################################################
# New Relic
#NEW_RELIC_APP_NAME=fsdata
#NEW_RELIC_LICENSE_KEY=

########################################################################################################################
# NLP
NLP_MM2_DB_CONNECT_URL=postgres://holger@localhost:5432/mm_conversations
NLP_MM2_DB_USES_SSL=false
NLP_BERT_SERVICE_URL="https://48f3-60-254-5-109.ngrok-free.app/classify/"

########################################################################################################################
# Redis:
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_URL=redis://localhost:6379
REDIS_APP_EVENTS_PUB_DB_INDEX=2
REDIS_APP_EVENTS_SUB_DB_INDEX=3
REDIS_MESSAGE_BUS_DB_INDEX=4
REDIS_SESSION_STORE_DB_INDEX=1
REDIS_TEMP_DATA_DB_INDEX=5
REDIS_APP_GRAPHQL_RATE_LIMITS_DB_INDEX=6
REDIS_APP_ROUTES_RATE_LIMITS_DB_INDEX=7
REDIS_ANALYTICS_DB_INDEX=8

########################################################################################################################
# Rollbar:
#ROLLBAR_SERVER_TOKEN=

########################################################################################################################
# SecureID:
SECURE_ID_REQUIRE_EMAIL=false
SECURE_ID_REQUIRE_PHONE_NUMBER=false
SECURE_ID_EMAIL_MUST_BE_UNIQUE=true
SECURE_ID_PHONE_NUMBER_MUST_BE_UNIQUE=true
SECURE_ID_ALLOW_TESTING_CONFIRM_TOKENS=true
SECURE_ID_REQUIRE_PASSWORD_TO_UPDATE_MFA_PROPS=true

########################################################################################################################
# Tracking:
#TRACKING_META_ACCESS_TOKEN='EAALAD1atas4BOyAsUb6VDAQt546PqiI8YzhNjgJiVVQEdV6kpqMU0lHapDWMdJRIZB3y6rg4r8CZAGHjpd10tAumeEW6G5HMr0GFdBPKHrJX5P2TIzzXuMbZC9EbH1JQRG1sdviedUPPif7TGAieCe9rZCKPMcksyPyqvm2SLJ9O1UNNbZCthi8blsRck481e'

########################################################################################################################
## Services:
ENABLE_SERVICE_ACCOUNTS=true
ENABLE_SERVICE_ANALYTICS=false
ENABLE_SERVICE_APP_EVENTS=true
ENABLE_SERVICE_ASSETS=true
ENABLE_SERVICE_AWS=true
ENABLE_SERVICE_CHANNELS=true
ENABLE_SERVICE_DATA_GENERATOR=false
ENABLE_SERVICE_DB=true
ENABLE_SERVICE_FIREBASE=true
ENABLE_SERVICE_GRAPHQL_API=true
ENABLE_SERVICE_HTTP=true
ENABLE_SERVICE_I18N=true
ENABLE_SERVICE_LOGGER=true
ENABLE_SERVICE_MATCHING=true
ENABLE_SERVICE_MESSAGE_BUS=true
ENABLE_SERVICE_MESSAGING=true
ENABLE_SERVICE_MM2=false
ENABLE_SERVICE_MODELS=true
ENABLE_SERVICE_NLP=false
ENABLE_SERVICE_REDIS=true
ENABLE_SERVICE_REST_API=false
ENABLE_SERVICE_SLACK=false
ENABLE_SERVICE_SYSTEM=true