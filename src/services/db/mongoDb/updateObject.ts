import { EnhancedOmit, InferIdType, WithId, WithoutId } from 'mongodb'

import { BaseObjectWithDbId } from './types/index.js'
import { ErrorCode } from '../../../app/types/index.js'
import { IBaseModel } from '../../models/types/IBaseModel.js'
import { ModelType } from '../../../app/types/ModelType.js'
import { ServiceRequest } from '../../models/types/classes/ServiceRequest.js'
import { UpdateObjectOptions } from '../../models/types/classes/UpdateObjectOptions.js'
import findObjectById from './findObjectById.js'
import helpers from './helpers/index.js'
import logger from '../../logger/index.js'

declare type WithOptionalId<TSchema> = EnhancedOmit<TSchema, '_id'> & {
  _id?: InferIdType<TSchema>;
};

async function updateObject<M extends IBaseModel>(
  modelType: ModelType,
  changes: Partial<M>,
  options: UpdateObjectOptions | null | undefined,
  serviceRequest: ServiceRequest,
): Promise<M> {
  const collection = helpers.getModelCollection(modelType)

  if (!collection) {
    logger.error('services.db.mongoDb.updateObject: no collection found for model',
      { modelType }, { remote: true })
    throw new Error(ErrorCode.systemError)
  }

  if (!(changes as BaseObjectWithDbId<M>)._id && !changes.id) {
    logger.error('services.db.mongoDb.updateObject: no ID given.',
      { changes }, { remote: true })
    throw new Error(ErrorCode.systemError)
  }

  const dbFormattedObject = helpers.formatData<M>(changes, true) as WithId<M>
  const _id = (dbFormattedObject as BaseObjectWithDbId<M>)._id

  if (!_id) {
    logger.error('services.db.mongoDb.updateObject: failed to build _id.',
      { changes }, { remote: true })
    throw new Error(ErrorCode.systemError)
  }

  if (options && options.replace) {
    const result = await collection.replaceOne(
      { _id },
      dbFormattedObject,
      { upsert: options?.upsert === true },
    )

    if (!result || (result.modifiedCount !== 1 && result.upsertedCount !== 1)) {
      // todo: return a db specific error
      throw new Error('services.db.mongoDb.updateObject: failed (modifiedCount|upsertedCount != 1.')
    }
  } else {
    delete (dbFormattedObject as WithOptionalId<M>)._id // Ensure _id is not set in the update operation
    const result = await collection.updateOne(
      { _id },
      { $set: dbFormattedObject },
      { upsert: options?.upsert === true },
    )

    if (
      !result ||
      (result.matchedCount !== 1 && result.upsertedCount !== 1)
    ) {
      // Check if this is a case where the document doesn't exist (deleted user scenario)
      if (result && result.matchedCount === 0 && result.upsertedCount === 0 && !options?.upsert) {
        logger.warn('services.db.mongoDb.updateObject: document not found (possibly deleted), ignoring update.',
          { modelType, _id: _id.toString() })
        // Return the changes as-is instead of throwing
        return changes as M
      }

      logger.error('services.db.mongoDb.updateObject: unexpected result.',
        { modelType, _id: _id.toString(), result })
      throw new Error('services.db.mongoDb.updateObject: failed (matchedCount != 1).')
    }
  }

  if (options && options.returnReloadedObject === false) {
    return changes as M
  }

  return await findObjectById<M>(
    modelType,
    changes.id || _id.toString(),
    undefined,
    serviceRequest,
  ) as unknown as M
}

export default updateObject
