import { AppEventType } from '../../../../app/types/AppEventType.js'
import { <PERSON>rrorCode, ServiceRequestResult } from '../../../../app/types/index.js'
import { ModelType } from '../../../../app/types/ModelType.js'
import { ServiceName } from '../../../../app/types/enums.js'
import { Wallet } from '../../types/models/Wallet.js'
import { WalletItem } from '../../types/models/WalletItem.js'
import { WalletItemTransfer } from '../../types/models/WalletItemTransfer.js'
import * as Accounts from '../../../accounts/types/index.js'
import * as Messaging from '../../../messaging/types/index.js'
import * as Models from '../../../models/types/index.js'
import appHelpers from '../../../../app/helpers.js'
import logger from '../../../logger/index.js'

const sendWalletItemTransferNotification = async (
  walletItemTransferId: string,
  serviceRequest: Models.ServiceRequest,
  sender?: Accounts.User | null,
  wallet?: Wallet | null,
  walletItem?: WalletItem | null,
  walletItemTransfer?: WalletItemTransfer | null,
): Promise<Models.ServiceRequest> => {
  const models = appHelpers.getCoreService<Models.IModelsService>(ServiceName.models)

  if (!walletItemTransfer) {
    walletItemTransfer = await models.findObjectById<WalletItemTransfer>(
      ModelType.WalletItemTransfer,
      walletItemTransferId,
      undefined,
      serviceRequest,
    )

    if (!walletItemTransfer) {
      logger.error('services.wallet.sendWalletItemTransferNotification: walletItemTransfer not found.',
        { walletItemTransferId }, { remote: true })

      await appHelpers.updateServiceRequest(
        serviceRequest,
        {
          result: ServiceRequestResult.error,
          errorCode: ErrorCode.invalidInput,
          message: 'walletItemTransfer not found',
        },
        [walletItemTransferId],
        [ModelType.WalletItemTransfer],
        true,
      )
      throw new Error(ErrorCode.notFound)
    }
  }

  if (!walletItem) {
    walletItem = await models.findObjectById<WalletItem>(
      ModelType.WalletItem,
      walletItemTransfer.walletItemId,
      undefined,
      serviceRequest,
    )

    if (!walletItem) {
      logger.error('services.wallet.sendWalletItemTransferNotification: walletItem not found.',
        { walletItemTransferId, walletItemId: walletItemTransfer.walletItemId }, { remote: true })

      await appHelpers.updateServiceRequest(
        serviceRequest,
        {
          result: ServiceRequestResult.error,
          errorCode: ErrorCode.invalidInput,
          message: 'walletItem not found',
        },
        [walletItemTransferId],
        [ModelType.WalletItemTransfer],
        true,
      )
      throw new Error(ErrorCode.notFound)
    }
  }

  if (!wallet) {
    wallet = await models.findObjectById<Wallet>(
      ModelType.Wallet,
      walletItem.walletId,
      undefined,
      serviceRequest,
    )

    if (!wallet) {
      logger.error('services.wallet.createWalletItemTransfer: wallet not found.',
        { walletItemTransferId }, { remote: true })

      await appHelpers.updateServiceRequest(
        serviceRequest,
        {
          result: ServiceRequestResult.error,
          errorCode: ErrorCode.invalidInput,
          message: 'wallet not found',
        },
        [walletItemTransferId],
        [ModelType.WalletItemTransfer],
        true,
      )
      throw new Error(ErrorCode.notFound)
    }
  }

  const senderId = wallet.id

  if (!sender) {
    sender = await models.findObjectById<Accounts.User>(
      ModelType.User,
      senderId,
      undefined,
      serviceRequest,
    )

    if (!sender) {
      logger.error('services.wallet.createWalletItemTransfer: sender not found.',
        { walletItemTransferId }, { remote: true })

      await appHelpers.updateServiceRequest(
        serviceRequest,
        {
          result: ServiceRequestResult.error,
          errorCode: ErrorCode.invalidInput,
          message: 'sender user not found',
        },
        undefined,
        [ModelType.WalletItemTransfer],
        true,
      )
      throw new Error(ErrorCode.notFound)
    }
  }

  const messaging = appHelpers.getCoreService<Messaging.IMessagingService>(ServiceName.messaging)

  const notificationContext = {
    recipientEmail: walletItemTransfer.recipientEmail,
    recipientFullName: walletItemTransfer.recipientFullName || walletItemTransfer.recipientEmail,
    senderId,
    walletItemName: walletItem.name,
    walletItemBalance: walletItem.balance,
    transferId: walletItemTransfer.id,
    messageText: walletItemTransfer.messageText || `You have received a ${walletItem.name} with balance ${walletItem.balance}`,
    subjectText: walletItemTransfer.subjectText || `Wallet Item Transfer: ${walletItem.name}`,
  }

  const notification = await messaging.createNotification(
    {
      notificationType: Messaging.NotificationType.walletItemTransfer,
      title: `Wallet Item Transfer: ${walletItemTransfer.subjectText}`,
      messageText: notificationContext.messageText,
      sendEmail: true,
      sendInAppMessage: true,
      context: notificationContext,
    },
    sender, // recipient will be determined by email
    true, // apply template
    true, // send out immediately
    serviceRequest,
  )

  if (!notification) {
    logger.error('services.wallet.sendWalletItemTransferNotification: sending failed.',
      { walletItemTransfer }, { remote: true })

    await appHelpers.updateServiceRequest(
      serviceRequest,
      {
        result: ServiceRequestResult.error,
        errorCode: ErrorCode.systemError,
        message: 'Failed to create notification for wallet item transfer',
      },
      [walletItemTransferId],
      [ModelType.WalletItemTransfer],
      true,
    )

    return serviceRequest
  }

  if (notification.sentMessagesCount < 1) {
    logger.error('services.wallet.sendWalletItemTransferNotification: no messages sent.',
      { notification }, { remote: true })

    await appHelpers.updateServiceRequest(
      serviceRequest,
      {
        result: ServiceRequestResult.error,
        errorCode: ErrorCode.systemError,
        message: 'Failed to send notification',
      },
      [walletItemTransferId, notification.id],
      [ModelType.WalletItemTransfer, ModelType.Notification],
      true,
    )

    return serviceRequest
  }

  await models.updateObject<WalletItemTransfer>(
    ModelType.WalletItemTransfer,
    {
      id: walletItemTransferId,
      notificationId: notification.id,
      sentAt: new Date(),
    },
    { returnReloadedObject: false},
    serviceRequest,
  )

  appHelpers.publishAppEvent<Models.ObjectChangedAppEventData>(
    AppEventType.objectChanged,
    {
      objectId: walletItemTransferId,
      modelType: ModelType.WalletItemTransfer,
      messageType: Models.ObjectChangedEventType.updated,
      ownerUserId: senderId,
      serviceRequest,
    },
  )

  await appHelpers.updateServiceRequest(
    serviceRequest,
    {
      result: ServiceRequestResult.ok,
    },
    [walletItemTransferId, notification.id],
    [ModelType.WalletItemTransfer, ModelType.Notification],
    true,
  )

  return serviceRequest
}

export default sendWalletItemTransferNotification
